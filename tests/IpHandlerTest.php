<?php

use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\CheckVarHandlers\IpHandler;
use Imponeer\DataFilter\Enums\IpHandlerMode;

class IpHandlerTest extends TestCase
{
    private IpHandler $handler;

    protected function setUp(): void
    {
        $this->handler = new IpHandler();
    }

    public function testAutoconfigureWithValidEnumValues()
    {
        $options1 = 'ipv4';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertInstanceOf(IpHandlerMode::class, $options1);
        $this->assertEquals(IpHandlerMode::IPV4, $options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithInvalidValues()
    {
        $options1 = 'invalid';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertEquals(IpHandlerMode::IPV4, $options1); // Should default to IPV4
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithAllValidModes()
    {
        $testCases = [
            'ipv4' => IpHandlerMode::IPV4,
            'ipv6' => IpHandlerMode::IPV6,
            'rfc' => IpHandlerMode::RFC,
            'res' => IpHandlerMode::RES,
        ];

        foreach ($testCases as $input => $expected) {
            $options1 = $input;
            $options2 = '';
            
            $this->handler->autoconfigure($options1, $options2);
            
            $this->assertEquals($expected, $options1, "Failed for input: $input");
        }
    }

    public function testCheckWithEnumValues()
    {
        $ipv4Address = '***********';
        $ipv6Address = '2001:0db8:85a3:0000:0000:8a2e:0370:7334';
        $privateIp = '***********';
        $publicIp = '*******';
        $reservedIp = '127.0.0.1';
        
        // Test IPV4 mode
        $result = $this->handler->check($ipv4Address, IpHandlerMode::IPV4, '');
        $this->assertEquals($ipv4Address, $result);
        
        $result = $this->handler->check($ipv6Address, IpHandlerMode::IPV4, '');
        $this->assertFalse($result);
        
        // Test IPV6 mode
        $result = $this->handler->check($ipv6Address, IpHandlerMode::IPV6, '');
        $this->assertEquals($ipv6Address, $result);
        
        $result = $this->handler->check($ipv4Address, IpHandlerMode::IPV6, '');
        $this->assertFalse($result);
        
        // Test RFC mode (no private range)
        $result = $this->handler->check($publicIp, IpHandlerMode::RFC, '');
        $this->assertEquals($publicIp, $result);
        
        $result = $this->handler->check($privateIp, IpHandlerMode::RFC, '');
        $this->assertFalse($result);
        
        // Test RES mode (no reserved range)
        $result = $this->handler->check($publicIp, IpHandlerMode::RES, '');
        $this->assertEquals($publicIp, $result);
        
        $result = $this->handler->check($reservedIp, IpHandlerMode::RES, '');
        $this->assertFalse($result);
    }

    public function testCheckWithDefaultCase()
    {
        $validIp = '***********';
        $invalidIp = 'not-an-ip';
        
        // Test with null (should default to basic FILTER_VALIDATE_IP)
        $result = $this->handler->check($validIp, null, '');
        $this->assertEquals($validIp, $result);
        
        $result = $this->handler->check($invalidIp, null, '');
        $this->assertFalse($result);
    }

    public function testEnumValues()
    {
        $this->assertEquals('ipv4', IpHandlerMode::IPV4->value);
        $this->assertEquals('ipv6', IpHandlerMode::IPV6->value);
        $this->assertEquals('rfc', IpHandlerMode::RFC->value);
        $this->assertEquals('res', IpHandlerMode::RES->value);
    }

    public function testEnumTryFrom()
    {
        // Test valid string values
        $this->assertEquals(IpHandlerMode::IPV4, IpHandlerMode::tryFrom('ipv4'));
        $this->assertEquals(IpHandlerMode::IPV6, IpHandlerMode::tryFrom('ipv6'));
        $this->assertEquals(IpHandlerMode::RFC, IpHandlerMode::tryFrom('rfc'));
        $this->assertEquals(IpHandlerMode::RES, IpHandlerMode::tryFrom('res'));
        
        // Test invalid string
        $this->assertNull(IpHandlerMode::tryFrom('invalid'));
    }

    public function testSpecificIpValidationBehaviors()
    {
        // Test various IP addresses
        $testCases = [
            // Valid IPv4 addresses
            ['***********', IpHandlerMode::IPV4, '***********'],
            ['*******', IpHandlerMode::IPV4, '*******'],
            ['127.0.0.1', IpHandlerMode::IPV4, '127.0.0.1'],
            
            // Valid IPv6 addresses
            ['2001:0db8:85a3:0000:0000:8a2e:0370:7334', IpHandlerMode::IPV6, '2001:0db8:85a3:0000:0000:8a2e:0370:7334'],
            ['::1', IpHandlerMode::IPV6, '::1'],
            
            // Invalid IP addresses
            ['256.256.256.256', IpHandlerMode::IPV4, false],
            ['not-an-ip', IpHandlerMode::IPV4, false],
            ['invalid-ipv6', IpHandlerMode::IPV6, false],
        ];
        
        foreach ($testCases as [$ip, $mode, $expected]) {
            $result = $this->handler->check($ip, $mode, '');
            $this->assertEquals($expected, $result, "Failed for IP: $ip with mode: {$mode->value}");
        }
    }

    public function testPrivateAndReservedRangeFiltering()
    {
        // Private IP ranges (should be rejected by RFC mode)
        $privateIps = [
            '********',      // Class A private
            '**********',    // Class B private
            '***********',   // Class C private
        ];
        
        foreach ($privateIps as $privateIp) {
            $result = $this->handler->check($privateIp, IpHandlerMode::RFC, '');
            $this->assertFalse($result, "Private IP $privateIp should be rejected by RFC mode");
        }
        
        // Reserved IP ranges (should be rejected by RES mode)
        $reservedIps = [
            '127.0.0.1',     // Loopback
            '0.0.0.0',       // This network
        ];
        
        foreach ($reservedIps as $reservedIp) {
            $result = $this->handler->check($reservedIp, IpHandlerMode::RES, '');
            $this->assertFalse($result, "Reserved IP $reservedIp should be rejected by RES mode");
        }
        
        // Public IP (should pass both RFC and RES modes)
        $publicIp = '*******';
        $this->assertEquals($publicIp, $this->handler->check($publicIp, IpHandlerMode::RFC, ''));
        $this->assertEquals($publicIp, $this->handler->check($publicIp, IpHandlerMode::RES, ''));
    }

    public function testEmptyAndNullInputs()
    {
        // Test with empty string
        $result = $this->handler->check('', IpHandlerMode::IPV4, '');
        $this->assertFalse($result);
        
        // Test with null input
        $result = $this->handler->check(null, IpHandlerMode::IPV4, '');
        $this->assertFalse($result);
    }
}
