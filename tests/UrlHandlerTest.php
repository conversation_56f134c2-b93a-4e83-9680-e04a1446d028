<?php

use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\CheckVarHandlers\UrlHandler;
use Imponeer\DataFilter\Enums\UrlValidationType;

class UrlHandlerTest extends TestCase
{
    private UrlHandler $handler;

    protected function setUp(): void
    {
        $this->handler = new UrlHandler();
    }

    public function testAutoconfigureWithValidEnumValues()
    {
        $options1 = 'scheme';
        $options2 = 1;
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertInstanceOf(UrlValidationType::class, $options1);
        $this->assertEquals(UrlValidationType::SCHEME, $options1);
        $this->assertEquals(1, $options2);
    }

    public function testAutoconfigureWithInvalidValues()
    {
        $options1 = 'invalid';
        $options2 = 'invalid';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertNull($options1);
        $this->assertEquals(0, $options2);
    }

    public function testAutoconfigureWithAllValidTypes()
    {
        $testCases = [
            'scheme' => UrlValidationType::SCHEME,
            'host' => UrlValidationType::HOST,
            'path' => UrlValidationType::PATH,
            'query' => UrlValidationType::QUERY,
        ];

        foreach ($testCases as $input => $expected) {
            $options1 = $input;
            $options2 = 0;
            
            $this->handler->autoconfigure($options1, $options2);
            
            $this->assertEquals($expected, $options1, "Failed for input: $input");
        }
    }

    public function testCheckWithEnumValues()
    {
        // Test with a valid URL
        $url = 'https://example.com/path?query=value';
        
        // Test SCHEME validation
        $result = $this->handler->check($url, UrlValidationType::SCHEME, 0);
        $this->assertEquals($url, $result);
        
        // Test HOST validation
        $result = $this->handler->check($url, UrlValidationType::HOST, 0);
        $this->assertEquals($url, $result);
        
        // Test PATH validation
        $result = $this->handler->check($url, UrlValidationType::PATH, 0);
        $this->assertEquals($url, $result);
        
        // Test QUERY validation
        $result = $this->handler->check($url, UrlValidationType::QUERY, 0);
        $this->assertEquals($url, $result);
    }

    public function testCheckWithInvalidUrl()
    {
        $invalidUrl = 'not-a-url';
        
        $result = $this->handler->check($invalidUrl, UrlValidationType::SCHEME, 0);
        $this->assertFalse($result);
    }

    public function testCheckWithUrlEncoding()
    {
        $url = 'https://example.com/path with spaces';

        $result = $this->handler->check($url, UrlValidationType::SCHEME, 1);
        $this->assertNotEquals($url, $result);
        // The result should be URL encoded
        $this->assertStringContainsString('%', $result);
    }

    public function testEnumValues()
    {
        $values = UrlValidationType::values();
        $expected = ['scheme', 'host', 'path', 'query'];
        
        $this->assertEquals($expected, $values);
    }

    public function testEnumTryFromMixed()
    {
        // Test valid string
        $result = UrlValidationType::tryFromMixed('scheme');
        $this->assertEquals(UrlValidationType::SCHEME, $result);
        
        // Test invalid string
        $result = UrlValidationType::tryFromMixed('invalid');
        $this->assertNull($result);
        
        // Test non-string
        $result = UrlValidationType::tryFromMixed(123);
        $this->assertNull($result);
        
        // Test null
        $result = UrlValidationType::tryFromMixed(null);
        $this->assertNull($result);
    }
}
