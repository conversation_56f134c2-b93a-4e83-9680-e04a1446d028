<?php

use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\CheckVarHandlers\TextHandler;
use Imponeer\DataFilter\Enums\TextHandlerMode;
use Imponeer\DataFilter\DataFilter;

class TextHandlerTest extends TestCase
{
    private TextHandler $handler;
    private DataFilter $dataFilter;

    protected function setUp(): void
    {
        $this->dataFilter = $this->createMock(DataFilter::class);
        $this->handler = new TextHandler($this->dataFilter);
    }

    public function testAutoconfigureWithValidEnumValues()
    {
        $options1 = 'input';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertInstanceOf(TextHandlerMode::class, $options1);
        $this->assertEquals(TextHandlerMode::INPUT, $options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithInvalidValues()
    {
        $options1 = 'invalid';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertEquals(TextHandlerMode::INPUT, $options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithAllValidModes()
    {
        $testCases = [
            'input' => TextHandlerMode::INPUT,
            'output' => TextHandlerMode::OUTPUT,
            'print' => TextHandlerMode::PRINT,
        ];

        foreach ($testCases as $input => $expected) {
            $options1 = $input;
            $options2 = '';
            
            $this->handler->autoconfigure($options1, $options2);
            
            $this->assertEquals($expected, $options1, "Failed for input: $input");
        }
    }

    public function testCheckWithEnumValues()
    {
        $testData = '<script>alert("test")</script>Hello World';

        // Mock the DataFilter methods
        $this->dataFilter->method('filterTextareaInput')
            ->willReturn('filtered_input_data');
        $this->dataFilter->method('filterTextareaDisplay')
            ->willReturn('filtered_output_data');

        // Test INPUT mode
        $result = $this->handler->check($testData, TextHandlerMode::INPUT, '');
        $this->assertEquals('filtered_input_data', $result);

        // Test PRINT mode
        $result = $this->handler->check($testData, TextHandlerMode::PRINT, '');
        $this->assertEquals($testData, $result);

        // Test OUTPUT mode
        $result = $this->handler->check($testData, TextHandlerMode::OUTPUT, '');
        $this->assertEquals('filtered_output_data', $result);
    }

    public function testCheckWithDefaultCase()
    {
        $testData = 'Hello World';

        // Mock the DataFilter method
        $this->dataFilter->method('filterTextareaInput')
            ->willReturn('filtered_input_data');

        // Test with null (should default to INPUT behavior)
        $result = $this->handler->check($testData, null, '');
        $this->assertEquals('filtered_input_data', $result);
    }

    public function testEnumValues()
    {
        $this->assertEquals('input', TextHandlerMode::INPUT->value);
        $this->assertEquals('output', TextHandlerMode::OUTPUT->value);
        $this->assertEquals('print', TextHandlerMode::PRINT->value);
    }

    public function testEnumTryFrom()
    {
        // Test valid string values
        $this->assertEquals(TextHandlerMode::INPUT, TextHandlerMode::tryFrom('input'));
        $this->assertEquals(TextHandlerMode::OUTPUT, TextHandlerMode::tryFrom('output'));
        $this->assertEquals(TextHandlerMode::PRINT, TextHandlerMode::tryFrom('print'));
        
        // Test invalid string
        $this->assertNull(TextHandlerMode::tryFrom('invalid'));
    }
}
