<?php

use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\CheckVarHandlers\SpecialHandler;
use Imponeer\DataFilter\Enums\SpecialHandlerMode;

class SpecialHandlerTest extends TestCase
{
    private SpecialHandler $handler;

    protected function setUp(): void
    {
        $this->handler = new SpecialHandler();
    }

    public function testAutoconfigureWithValidEnumValues()
    {
        $options1 = 'striplow';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertInstanceOf(SpecialHandlerMode::class, $options1);
        $this->assertEquals(SpecialHandlerMode::STRIPLOW, $options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithInvalidValues()
    {
        $options1 = 'invalid';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertNull($options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithAllValidModes()
    {
        $testCases = [
            'striplow' => SpecialHandlerMode::STRIPLOW,
            'striphigh' => SpecialHandlerMode::STRIPHIGH,
            'encodehigh' => SpecialHandlerMode::ENCODEHIGH,
        ];

        foreach ($testCases as $input => $expected) {
            $options1 = $input;
            $options2 = '';
            
            $this->handler->autoconfigure($options1, $options2);
            
            $this->assertEquals($expected, $options1, "Failed for input: $input");
        }
    }

    public function testCheckWithEnumValues()
    {
        $testData = "Hello\x01\x02World\x80\x81&<>";
        
        // Test STRIPLOW mode - should remove low ASCII characters
        $result = $this->handler->check($testData, SpecialHandlerMode::STRIPLOW, '');
        $this->assertIsString($result);
        $this->assertStringNotContainsString("\x01", $result);
        $this->assertStringNotContainsString("\x02", $result);
        
        // Test STRIPHIGH mode - should remove high ASCII characters
        $result = $this->handler->check($testData, SpecialHandlerMode::STRIPHIGH, '');
        $this->assertIsString($result);
        $this->assertStringNotContainsString("\x80", $result);
        $this->assertStringNotContainsString("\x81", $result);
        
        // Test ENCODEHIGH mode - should encode high ASCII characters
        $result = $this->handler->check($testData, SpecialHandlerMode::ENCODEHIGH, '');
        $this->assertIsString($result);
        // High ASCII characters should be encoded
        $this->assertStringNotContainsString("\x80", $result);
        $this->assertStringNotContainsString("\x81", $result);
    }

    public function testCheckWithDefaultCase()
    {
        $testData = 'Hello & World <script>';

        // Test with null (should default to basic FILTER_SANITIZE_SPECIAL_CHARS)
        $result = $this->handler->check($testData, null, '');
        $this->assertIsString($result);
        // Should sanitize special characters using numeric entities
        $this->assertStringContainsString('&#38;', $result);
        $this->assertStringContainsString('&#60;', $result);
    }

    public function testEnumValues()
    {
        $this->assertEquals('striplow', SpecialHandlerMode::STRIPLOW->value);
        $this->assertEquals('striphigh', SpecialHandlerMode::STRIPHIGH->value);
        $this->assertEquals('encodehigh', SpecialHandlerMode::ENCODEHIGH->value);
    }

    public function testEnumTryFrom()
    {
        // Test valid string values
        $this->assertEquals(SpecialHandlerMode::STRIPLOW, SpecialHandlerMode::tryFrom('striplow'));
        $this->assertEquals(SpecialHandlerMode::STRIPHIGH, SpecialHandlerMode::tryFrom('striphigh'));
        $this->assertEquals(SpecialHandlerMode::ENCODEHIGH, SpecialHandlerMode::tryFrom('encodehigh'));
        
        // Test invalid string
        $this->assertNull(SpecialHandlerMode::tryFrom('invalid'));
    }

    public function testSpecificFilterBehaviors()
    {
        // Test data with various special characters and control characters
        $lowControlData = "Hello\x01\x02\x03World";
        $highControlData = "Hello\x80\x81\x82World";
        $specialCharsData = "Hello & <script> World";
        
        // Test STRIPLOW - should remove low ASCII control characters
        $result = $this->handler->check($lowControlData, SpecialHandlerMode::STRIPLOW, '');
        $this->assertEquals('HelloWorld', $result);
        
        // Test STRIPHIGH - should remove high ASCII characters
        $result = $this->handler->check($highControlData, SpecialHandlerMode::STRIPHIGH, '');
        $this->assertEquals('HelloWorld', $result);
        
        // Test ENCODEHIGH - should encode high ASCII characters
        $result = $this->handler->check($highControlData, SpecialHandlerMode::ENCODEHIGH, '');
        $this->assertStringContainsString('Hello', $result);
        $this->assertStringContainsString('World', $result);
        // High ASCII characters should be encoded (not present as raw bytes)
        $this->assertStringNotContainsString("\x80", $result);
        $this->assertStringNotContainsString("\x81", $result);
        
        // Test default behavior with special characters
        $result = $this->handler->check($specialCharsData, null, '');
        $this->assertStringContainsString('&#38;', $result);
        $this->assertStringContainsString('&#60;', $result);
    }

    public function testEmptyAndNullInputs()
    {
        // Test with empty string
        $result = $this->handler->check('', SpecialHandlerMode::STRIPLOW, '');
        $this->assertEquals('', $result);
        
        // Test with null input (should be handled gracefully)
        $result = $this->handler->check(null, SpecialHandlerMode::STRIPLOW, '');
        $this->assertIsString($result);
    }
}
