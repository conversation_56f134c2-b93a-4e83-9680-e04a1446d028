<?php

use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\CheckVarHandlers\StrHandler;
use Imponeer\DataFilter\Enums\StrHandlerMode;

class StrHandlerTest extends TestCase
{
    private StrHandler $handler;

    protected function setUp(): void
    {
        $this->handler = new StrHandler();
    }

    public function testAutoconfigureWithValidEnumValues()
    {
        $options1 = 'noencode';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertInstanceOf(StrHandlerMode::class, $options1);
        $this->assertEquals(StrHandlerMode::NOENCODE, $options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithInvalidValues()
    {
        $options1 = 'invalid';
        $options2 = '';
        
        $this->handler->autoconfigure($options1, $options2);
        
        $this->assertNull($options1);
        $this->assertEquals('', $options2);
    }

    public function testAutoconfigureWithAllValidModes()
    {
        $testCases = [
            'noencode' => StrHandlerMode::NOENCODE,
            'striplow' => StrHandlerMode::STRIPLOW,
            'striphigh' => StrHandlerMode::STRIPHIGH,
            'encodelow' => StrHandlerMode::ENCODELOW,
            'encodehigh' => StrHandlerMode::ENCODEHIGH,
            'encodeamp' => StrHandlerMode::ENCODEAMP,
        ];

        foreach ($testCases as $input => $expected) {
            $options1 = $input;
            $options2 = '';
            
            $this->handler->autoconfigure($options1, $options2);
            
            $this->assertEquals($expected, $options1, "Failed for input: $input");
        }
    }

    public function testCheckWithEnumValues()
    {
        $testData = '<script>alert("test")</script>Hello & World';

        // Test NOENCODE mode - strips tags then applies htmlspecialchars with ENT_NOQUOTES
        $result = $this->handler->check($testData, StrHandlerMode::NOENCODE, '');
        $this->assertEquals('alert("test")Hello &amp; World', $result);
        $this->assertStringNotContainsString('<script>', $result);

        // Test STRIPLOW mode
        $result = $this->handler->check($testData, StrHandlerMode::STRIPLOW, '');
        $this->assertIsString($result);
        $this->assertEquals('alert("test")Hello & World', $result);

        // Test STRIPHIGH mode
        $result = $this->handler->check($testData, StrHandlerMode::STRIPHIGH, '');
        $this->assertIsString($result);
        $this->assertEquals('alert("test")Hello & World', $result);

        // Test ENCODELOW mode
        $result = $this->handler->check($testData, StrHandlerMode::ENCODELOW, '');
        $this->assertIsString($result);
        $this->assertEquals('alert("test")Hello & World', $result);

        // Test ENCODEHIGH mode
        $result = $this->handler->check($testData, StrHandlerMode::ENCODEHIGH, '');
        $this->assertIsString($result);
        $this->assertEquals('alert("test")Hello & World', $result);

        // Test ENCODEAMP mode
        $result = $this->handler->check($testData, StrHandlerMode::ENCODEAMP, '');
        $this->assertIsString($result);
        $this->assertEquals('alert("test")Hello &#38; World', $result);
    }

    public function testCheckWithDefaultCase()
    {
        $testData = '<script>alert("test")</script>Hello & World';

        // Test with null (should default to htmlspecialchars behavior)
        $result = $this->handler->check($testData, null, '');
        $this->assertEquals('alert("test")Hello &amp; World', $result);
        $this->assertStringNotContainsString('<script>', $result);
    }

    public function testEnumValues()
    {
        $this->assertEquals('noencode', StrHandlerMode::NOENCODE->value);
        $this->assertEquals('striplow', StrHandlerMode::STRIPLOW->value);
        $this->assertEquals('striphigh', StrHandlerMode::STRIPHIGH->value);
        $this->assertEquals('encodelow', StrHandlerMode::ENCODELOW->value);
        $this->assertEquals('encodehigh', StrHandlerMode::ENCODEHIGH->value);
        $this->assertEquals('encodeamp', StrHandlerMode::ENCODEAMP->value);
    }

    public function testEnumTryFrom()
    {
        // Test valid string values
        $this->assertEquals(StrHandlerMode::NOENCODE, StrHandlerMode::tryFrom('noencode'));
        $this->assertEquals(StrHandlerMode::STRIPLOW, StrHandlerMode::tryFrom('striplow'));
        $this->assertEquals(StrHandlerMode::STRIPHIGH, StrHandlerMode::tryFrom('striphigh'));
        $this->assertEquals(StrHandlerMode::ENCODELOW, StrHandlerMode::tryFrom('encodelow'));
        $this->assertEquals(StrHandlerMode::ENCODEHIGH, StrHandlerMode::tryFrom('encodehigh'));
        $this->assertEquals(StrHandlerMode::ENCODEAMP, StrHandlerMode::tryFrom('encodeamp'));
        
        // Test invalid string
        $this->assertNull(StrHandlerMode::tryFrom('invalid'));
    }

    public function testSpecificFilterBehaviors()
    {
        // Test data with various characters
        $testData = "Hello\x01\x02World\x80\x81&<>";
        
        // Test STRIPLOW - should remove low ASCII characters
        $result = $this->handler->check($testData, StrHandlerMode::STRIPLOW, '');
        $this->assertStringNotContainsString("\x01", $result);
        $this->assertStringNotContainsString("\x02", $result);
        
        // Test STRIPHIGH - should remove high ASCII characters
        $result = $this->handler->check($testData, StrHandlerMode::STRIPHIGH, '');
        $this->assertStringNotContainsString("\x80", $result);
        $this->assertStringNotContainsString("\x81", $result);
        
        // Test ENCODEAMP - should encode ampersands
        $ampData = "Hello & World";
        $result = $this->handler->check($ampData, StrHandlerMode::ENCODEAMP, '');
        $this->assertStringContainsString('&#38;', $result);
    }
}
