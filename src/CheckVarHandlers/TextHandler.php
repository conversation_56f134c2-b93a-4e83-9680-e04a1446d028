<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Enums\TextHandlerMode;
use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\DataFilter;

class <PERSON>H<PERSON>ler implements HandlerInterface {

    public function __construct(
        private readonly DataFilter $dataFilter,
    )
    {
    }

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $options2 = '';
        $options1 = TextHandlerMode::tryFrom($options1) ?? TextHandlerMode::INPUT;
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        return match ($options1) {
            TextHandlerMode::OUTPUT => $this->dataFilter->filterTextareaDisplay($data),
            TextHandlerMode::PRINT => $data,
            TextHandlerMode::INPUT => $this->dataFilter->filterTextareaInput($data),
            default => $this->dataFilter->filterTextareaInput($data),
        };
    }
}
