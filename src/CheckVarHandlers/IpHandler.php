<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Enums\IpHandlerMode;
use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>p<PERSON><PERSON>ler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $options2 = '';
        $options1 = IpHandlerMode::tryFrom($options1) ?? IpHandlerMode::IPV4;
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        return match ($options1) {
            IpHandlerMode::IPV4 => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4),
            IpHandlerMode::IPV6 => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6),
            IpHandlerMode::RFC => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE),
            IpHandlerMode::RES => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE),
            default => filter_var($data, FILTER_VALIDATE_IP),
        };
    }
}
