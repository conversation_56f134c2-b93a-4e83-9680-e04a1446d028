<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Enums\SpecialHandlerMode;
use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON><PERSON><PERSON>ler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $options2 = '';
        $options1 = SpecialHandlerMode::tryFrom($options1);
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        return match ($options1) {
            SpecialHandlerMode::STRIPLOW => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_STRIP_LOW),
            SpecialHandlerMode::STRIPHIGH => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_STRIP_HIGH),
            SpecialHandlerMode::ENCODEHIGH => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_ENCODE_HIGH),
            default => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS),
        };
    }
}
