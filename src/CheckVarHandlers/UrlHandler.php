<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Enums\UrlValidationType;
use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>rlHandler implements HandlerInterface {
    public function autoconfigure(mixed &$options1, mixed &$options2): void {
        $valid_options2 = [0, 1];

        $options1 = UrlValidationType::tryFrom($options1);

        if ($options2 === '' || !in_array($options2, $valid_options2, true)) {
            $options2 = 0;
        } else {
            $options2 = 1;
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        $data = filter_var($data, FILTER_SANITIZE_URL);

        $options1 = $options1 instanceof UrlValidationType ? $options1 : UrlValidationType::tryFrom($options1);

        $valid = match ($options1) {
            UrlValidationType::PATH => filter_var($data, FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED),
            UrlValidationType::QUERY => filter_var($data, FILTER_VALIDATE_URL, FILTER_FLAG_QUERY_REQUIRED),
            default => filter_var($data, FILTER_VALIDATE_URL),
        };
        if ($valid) {
            if (isset($options2) && $options2) {
                return filter_var($data, FILTER_SANITIZE_ENCODED);
            }
            return $data;
        }
        return false;
    }
}
