<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Enums\StrHandlerMode;
use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>rHandler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $options2 = '';
        $options1 = StrHandlerMode::tryFrom($options1);
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        // returns $string
        return match ($options1) {
            StrHandlerMode::NOENCODE => htmlspecialchars(strip_tags($data), ENT_NOQUOTES),
            StrHandlerMode::STRIPLOW => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW),
            StrHandlerMode::STRIPHIGH => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_HIGH),
            StrHandlerMode::ENCODELOW => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_LOW),
            StrHandlerMode::ENCODEHIGH => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_HIGH),
            StrHandlerMode::ENCODEAMP => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_AMP),
            default => htmlspecialchars(strip_tags($data), ENT_NOQUOTES),
        };
    }
}
